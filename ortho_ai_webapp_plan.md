# 📄 Project Plan: Conversational Orthopaedics AI Webapp

---

## 📝 Overview

This project builds a **full-stack webapp** for answering orthopaedic-related questions using a powerful hybrid **RAG (Retrieval-Augmented Generation)** system. It combines:

- 🔍 Semantic retrieval from your **custom orthopaedics knowledge base** (using FAISS + Sentence Transformers)
- 🦙 **Local LLM** inference with **Ollama** (running Mistral)
- ☁️ **Fallback to OpenRouter** using **DeepSeek model** for robust answers
- 🗣 **Human-like speech** using **Coqui TTS**
- 🚀 A simple **React (or plain HTML/JS) frontend** for chat + audio

---

## ⚙️ Architecture

```
[ React UI ]
     |
     | HTTP
     v
[ FastAPI Server ]
     |
     |-> [FAISS vector DB for semantic retrieval]
     |-> [Ollama LLM locally]
     |-> [Fallback to DeepSeek via OpenRouter API]
     |-> [Coqui TTS for voice output]
     |
[ Returns text + audio ]
```

---

## 🛠 Tech Stack

✅ **Backend**: Python, FastAPI\
✅ **Vector store**: FAISS\
✅ **Embeddings**: SentenceTransformers (`all-MiniLM-L6-v2`)\
✅ **LLM**: Local Ollama running `mistral`\
✅ **Fallback LLM**: OpenRouter API with DeepSeek model\
✅ **TTS**: Coqui TTS (local, human-like)\
✅ **Frontend**: React or simple HTML/JS

---

## 🏗 Implementation Steps

### 1️⃣ Set up environment

- Python venv

```bash
python -m venv venv
source venv/bin/activate
pip install fastapi uvicorn[standard] requests numpy faiss-cpu sentence-transformers TTS python-dotenv
```

---

### 2️⃣ Build semantic index

- Use Sentence Transformers to embed orthopaedic documents.
- Store in FAISS index + save docs list.

```python
from sentence_transformers import SentenceTransformer
import faiss, numpy as np, pickle

model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
docs = ["Osteoarthritis...", "ACL reconstruction...", "Rotator cuff..."]
embeddings = model.encode(docs)

index = faiss.IndexFlatL2(embeddings.shape[1])
index.add(np.array(embeddings))
faiss.write_index(index, "kb.index")
with open("kb_docs.pkl", "wb") as f:
    pickle.dump(docs, f)
```

---

### 3️⃣ FastAPI backend with RAG

- Retrieve top-k similar chunks
- Try Ollama local API (Mistral)
- If answer too short / generic → fallback to OpenRouter with DeepSeek
- Generate TTS using Coqui

---

### 4️⃣ TTS integration

- Use `TTS` package to generate `speech.wav` from text answer.

```python
from TTS.api import TTS
tts = TTS(model_name="tts_models/en/vctk/vits").to("cpu")
tts.tts_to_file("Hello from ortho AI!", "static/answer.wav")
```

---

### 5️⃣ Frontend

- Input box to ask question.
- Shows text response.
- Plays audio automatically.

---

### 6️⃣ Environment variables

`.env` file with:

```
OPENROUTER_API_KEY=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
```

---

## 🚀 Prompt for AI Webapp Developer (like Replit)

> **Prompt:**
>
> Build a full-stack webapp with:
>
> - FastAPI backend
> - Uses local FAISS vector db and sentence-transformers for semantic retrieval
> - Calls local Ollama ([http://localhost:11434/api/generate](http://localhost:11434/api/generate)) with model "mistral"
> - If result is too short, fallback to OpenRouter using DeepSeek:
>   - model: "deepseek/deepseek-r1-0528\:free"
>   - API key from .env
> - Converts final answer to speech using Coqui TTS and serves audio file
> - Simple HTML frontend with input, shows text answer and auto-plays audio.

---

## ✅ Deliverables

- `main.py` → FastAPI app with RAG + TTS
- `build_index.py` → script to build your semantic DB
- `static/` → to serve `speech.wav`
- `.env` → with your OpenRouter key
- `index.html` → simple chat UI

---

🎉 **Done!**

If you want, I can prepare a **zip package with this boilerplate** so you can deploy it in seconds. Just say the word!

